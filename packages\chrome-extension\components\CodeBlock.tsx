import CodeCom from '@/entrypoints/popup/Dimension/code'
import type React from 'react'
import {useEffect, useState} from 'react'
import {codeToHtml} from 'shiki'

interface CodeBlockProps {
  title: string
  code: string
  lang: string
  className?: string
}

export const CodeBlock: React.FC<CodeBlockProps> = ({title, code, lang, className = ''}) => {
  const [highlightedCode, setHighlightedCode] = useState('')

  async function highlightCode() {
    if (code) {
      const highlightedCode = await codeToHtml(code, {
        lang,
        theme: 'vitesse-dark',
      })
      setHighlightedCode(highlightedCode)
    }
  }

  useEffect(() => {
    highlightCode()
  }, [code, lang])

  /* const highlightedCode = useMemo(() => {
    if (code) {
      // Use the appropriate language for syntax highlighting
      const language = Prism.languages[lang] || Prism.languages.css
      return Prism.highlight(code, language, lang)
    }
    return ''
  }, [code, lang])

  if (!code) {
    return null
  } */

  return (
    <div className={`mb-2 ${className}`}>
      <CodeCom highlightedCode={highlightedCode} copyResult={code} />
    </div>
  )
}

export default CodeBlock
