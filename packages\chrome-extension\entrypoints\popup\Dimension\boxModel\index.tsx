import {changeUnitDeep} from '@/lib/dimension'
import {ReportType, f2cDataReport} from '@/report/dataReport'
import dimensionStore from '@/store/dimensionStore'
import {copyToClipboard} from '@/utils/copyClipboard'
import {getFloat, getInt} from '@/utils/format-number-utils'
import {getNumericFloat} from '@/utils/format-number-utils'
import {useEffect, useRef} from 'react'
import borderRadiusIcon from '../assets/borderRadius.png'

const BoxModel = () => {
  const dimensionInfo = dimensionStore.clone(dimensionStore.state.dimensionInfo)
  const {currentMagnification, currentRemBase, currentVwBase, currentSelectUnit} = dimensionStore.state
  changeUnitDeep(dimensionInfo.css, currentSelectUnit, currentMagnification, currentRemBase, currentVwBase)
  const boxRef = useRef<HTMLDivElement>(null)
  useEffect(() => {
    if (!boxRef.current) return
    const bindCopyEvent = (evt: MouseEvent) => {
      let target = evt.target as HTMLElement | null | undefined
      if (!target?.dataset.copy) {
        target = target?.closest('[data-copy]')
      }
      if (!target) return
      if (target?.dataset.copy) {
        const innerText = target.innerText
        copyToClipboard(innerText).then(res => {
          // message.success(res)
          f2cDataReport(ReportType.copyCount, '', 1, {
            dim4: 'chrome',
          })
        })
      }
    }

    if (boxRef.current) {
      boxRef.current.addEventListener('click', bindCopyEvent)
    }
    return () => {
      boxRef.current?.removeEventListener('click', bindCopyEvent)
    }
  }, [boxRef.current])

  const posItem = (num: string | number, pos: string, className?: string) => {
    return (
      <div
        className={`${
          pos == 'left' || pos == 'right' ? 'h-4 pl-2 pr-2 relative  ' : 'w-fit h-8 absolute'
        } flex-center-h ${pos == 'top' && 'top-0 ml-auto mr-auto left-0 right-0'} ${
          pos == 'bottom' && 'bottom-0 left-0 right-0 ml-auto mr-auto'
        }`}
      >
        <div
          className={`${
            pos == 'left' || pos == 'right' ? 'w-full h-[1px] ' : 'w-[1px] h-full left-0 right-0 ml-auto mr-auto'
          } bg-[#FF7B73] absolute`}
        ></div>
        <div
          className={
            'w-fit h-[14px] flex-center-h bg-[#FF4C42] rounded-[2px] relative z-10 p-[2px] box-content' +
            (className ? ' ' + className : '')
          }
        >
          <span
            className="text-[#fff] text-[12px] text-center w-full overflow-hidden text-ellipsis"
            data-copy
            title={String(getFloat(num))}
          >
            {getInt(num)}
          </span>
        </div>
      </div>
    )
  }
  const borderItem = (num: string | number) => {
    return (
      <div data-copy className="text-xs min-w-[30px] text-center h-6 leading-6" title={String(getFloat(num))}>
        {Number(num) > 0 ? getInt(num) : '-'}
      </div>
    )
  }
  const paddingItem = (num: string | number) => {
    return (
      <div data-copy className="text-xs max-w-[36px] text-center h-6 leading-6 flex-1" title={String(getFloat(num))}>
        {Number(num) > 0 ? getInt(num) : '-'}
      </div>
    )
  }
  return (
    <div
      className={`w-full h-[192px] bg-[#F7F8FA] rounded relative ml-auto mr-auto flex-center-h flex-nowrap boxModel text-xs`}
      ref={boxRef}
    >
      <div className="w-[32px]">
        {Boolean(Number(dimensionInfo.css?.['left'])) &&
          posItem(dimensionInfo.css?.['left'] || 0, 'left', 'max-w-[28px]')}
      </div>
      <div className="w-[226px] grow h-full flex-center-h relative">
        {['top', 'bottom'].map(pos => {
          return Boolean(Number(dimensionInfo.css?.[pos])) ? (
            <div key={pos} data-copy>
              {posItem(dimensionInfo.css?.[pos], pos)}
            </div>
          ) : null
        })}
        <div
          className="flex-shrink h-[128px] bg-[#FFF] w-full flex-center-h relative"
          style={{
            border: '1px solid #F2F3F5',
            borderTopLeftRadius: dimensionInfo.css?.borderRadius?.['topLeftRadius'] > 0 ? '16px' : '',
            borderTopRightRadius: dimensionInfo.css?.borderRadius?.['topRightRadius'] > 0 ? '16px' : '',
            borderBottomLeftRadius: dimensionInfo.css?.borderRadius?.['bottomLeftRadius'] > 0 ? '16px' : '',
            borderBottomRightRadius: dimensionInfo.css?.borderRadius?.['bottomRightRadius'] > 0 ? '16px' : '',
          }}
        >
          {['topLeftRadius', 'topRightRadius', 'bottomLeftRadius', 'bottomRightRadius'].map((item, index) => {
            let posInfo = ''
            let containerPosInfo = ''
            if (!dimensionInfo.css?.borderRadius?.[item]) {
              return null
            } else {
              switch (item) {
                case 'topLeftRadius':
                  containerPosInfo = 'left-0 top-[1px] pt-[7px] pl-[8px] text-xs'
                  posInfo = 'left-0 top-0 rotate-[270deg]'
                  break
                case 'topRightRadius':
                  containerPosInfo = 'right-0 top-[1px] pt-[7px] pr-[8px] text-xs text-right justify-end'
                  posInfo = 'right-0 top-0 rotate-[0deg]'
                  break
                case 'bottomLeftRadius':
                  containerPosInfo = 'left-0 bottom-[1px] pb-[7px] pl-[8px] text-xs text-left items-end'
                  posInfo = 'left-0 bottom-0 rotate-[180deg]'
                  break
                case 'bottomRightRadius':
                  containerPosInfo = 'right-0 bottom-[1px] pb-[7px] pr-[8px] text-xs text-right items-end justify-end'
                  posInfo = 'right-0 bottom-0 rotate-[90deg]'
                  break
              }
            }
            return (
              <div className={`${containerPosInfo} w-9 h-9 absolute flex`} key={item}>
                <div data-copy className={'z-10'} title={String(getFloat(dimensionInfo.css.borderRadius?.[item]))}>
                  {getInt(dimensionInfo.css.borderRadius?.[item])}
                </div>
                <img src={borderRadiusIcon} width={13} className={`absolute ${posInfo}`} />
              </div>
            )
          })}
          {borderItem(dimensionInfo.css?.border?.['borderLeft'])}
          {['borderTop', 'borderBottom'].map(pos => {
            return (
              <div
                key={pos}
                className={`absolute ${pos == 'borderTop' ? 'top-0' : 'bottom-0'} h-6 ml-auto mr-auto left-0 right-0`}
              >
                {borderItem(dimensionInfo.css?.border?.[pos])}
              </div>
            )
          })}
          <div className="absolute text-disable text-[12px] top-1 left-9">Border</div>
          <div className="max-w-[168px] h-[80px] grow bg-primaryPale relative flex-center-h border border-solid border-disable ">
            <div className="absolute text-disable text-[12px] top-[5px] left-2">Padding</div>

            {paddingItem(dimensionInfo.css?.padding?.['paddingLeft'])}
            {['paddingTop', 'paddingBottom'].map(pos => {
              return (
                <div
                  key={pos}
                  className={`absolute ${
                    pos == 'paddingTop' ? 'top-0' : 'bottom-0'
                  } h-6 ml-auto mr-auto left-0 right-0 flex-center-h`}
                >
                  {paddingItem(dimensionInfo.css?.padding?.[pos])}
                </div>
              )
            })}
            <div
              className="min-w-[96px] whitespace-nowrap grow h-6 bg-[#FFF] text-xsm flex-center-h overflow-hidden"
              style={{
                borderRadius: '1px',
                border: '1px dashed #868D9C',
              }}
            >
              <div>
                <span data-copy title={String(getFloat(dimensionInfo.css?.width))}>
                  {getNumericFloat(dimensionInfo.css?.width)}
                </span>{' '}
                x{' '}
                <span data-copy title={String(getFloat(dimensionInfo.css?.height))}>
                  {getNumericFloat(dimensionInfo.css?.height)}
                </span>
              </div>
            </div>
            {paddingItem(dimensionInfo.css?.padding?.['paddingRight'])}
          </div>
          {borderItem(dimensionInfo.css?.border?.['borderRight'])}
        </div>
      </div>
      <div className="w-[32px]">
        {Boolean(Number(dimensionInfo.css?.['right'])) &&
          posItem(dimensionInfo.css?.['right'], 'right', 'max-w-[28px]')}
      </div>
    </div>
  )
}
export default BoxModel
