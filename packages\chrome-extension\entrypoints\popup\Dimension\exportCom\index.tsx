import Tooltip from '@/components/ui/toolTip'
import {ReportType, f2cDataReport} from '@/report/dataReport'
import dimensionStore from '@/store/dimensionStore'
import F2cEventType from '@/types/eventType'
// import Prism from 'prismjs'
import SVG from 'react-inlinesvg'
import CodeCom from '../code'

const ExportCom = () => {
  const [show, setShow] = useState('')
  const imgScale = useRef(1)
  const imgFormat = useRef('png')
  const {
    dimensionInfo: {nodeId, svg, isSvg},
  } = dimensionStore.state
  const svgCode = useMemo(() => {
    if (svg) {
      // return Prism.highlight(svg, Prism.languages.svg, 'svg')
    }
    return ''
  }, [svg])
  const previewImg = useCallback(
    (nodeId?: string) => {
      setShow(state => {
        if (state) {
          return ''
        } else {
          if (nodeId) {
            showPreview(nodeId)
            // handlePreviewClick(nodeId)
          }
          return state
        }
      })
    },
    [nodeId],
  )

  async function showPreview(id?: string) {
    if (!id) return

    const node = (await figma.getNodeByIdAsync(id)) as SceneNode
    if (node) {
      let svg = ''
      const svgData = await node.exportAsync({
        format: 'SVG',
        svgIdAttribute: true, // 可选：为 SVG 元素添加 ID 属性
        svgOutlineText: true, // 可选：将文本转换为轮廓
      })

      // 将 Uint8Array 转换为字符串
      const decoder = new TextDecoder('utf-8')
      svg = decoder.decode(svgData)
      setShow(svg)
    }
  }

  // const handlePreviewClick = (id?: string) => {
  //   if (imgFormat.current.toUpperCase() === 'PNG') {
  //     downLoadImg({nodeId: id, scale: imgScale.current, format: imgFormat.current}, true)
  //   }
  // }

  const compressImg = async (buf: Uint8Array<ArrayBufferLike>) => {
    const formData = new FormData()
    // Create a File object from the Uint8Array
    const file = new File([buf], `origin.png`, {type: 'image/png'})

    formData.append('file', file)

    try {
      const response = await fetch('https://compression-test.yy.com/api/image/compress', {
        method: 'POST',
        body: formData,
      })

      if (response.ok) {
        const compressedData = await response.arrayBuffer()
        const compressBuf = new Uint8Array(compressedData)
        return compressBuf
      } else {
        // It's good practice to handle non-ok responses
        console.error('Image compression failed with status:', response.status)
        const errorText = await response.text()
        console.error('Error details:', errorText)
        return buf
      }
    } catch (error) {
      console.error('Image compression request failed:', error)
      return buf
    }
  }

  async function downLoadImg(params: {nodeId?: string; scale: number; format: any}, isCompress = true) {
    console.log('isCompress', isCompress)
    const {nodeId, scale, format} = params
    if (!nodeId) return
    const node = (await figma.getNodeByIdAsync(nodeId)) as SceneNode
    if (node) {
      const imgFormat = format.toUpperCase()
      let buf = await node.exportAsync(
        imgFormat === 'PNG' || imgFormat === 'JPG'
          ? {
              format: imgFormat,
              constraint: {
                type: 'SCALE',
                value: scale,
              },
            }
          : {
              format: imgFormat,
            },
      )
      // 如果是png格式，调用 https://compression-test.yy.com/api/image/compress post接口进行压缩,payload是表单数据file: （二进制）；
      if (imgFormat === 'PNG' && isCompress) {
        buf = await compressImg(buf)
      }
      // 创建 Blob 对象
      const blob = new Blob([buf], {type: `image/${format}`})
      const url = URL.createObjectURL(blob)
      // window.open(url)
      // 创建下载链接并触发点击
      const a = document.createElement('a')
      a.href = url
      // if (isDownload) {

      a.download = `${node.name}.${format}`
      document.body.appendChild(a)
      a.click()

      // } else {
      //   window.open(url)
      // }
      // 清理
      setTimeout(() => {
        document.body.removeChild(a)
        URL.revokeObjectURL(url)
      }, 100)
    }
  }
  // useEffect(() => {
  //   if (show) {
  //       window.parent.postMessage({ type: F2cEventType.preview, payload: { nodeId} }, '*')
  //   }
  //   const messageHandler = (event: MessageEvent) => {
  //     if (event.data.type === F2cEventType.previewResp) {
  //       const data = event.data.payload
  //       if (data.nodeId === nodeId) {
  //         setShow(data.svg)
  //       }
  //     } else if(event.data.type === F2cEventType.downLoadResp){
  //       const data = event.data.payload
  //       const {buf, format} = data

  //       // 创建 Blob 对象
  //       const blob = new Blob([buf], {type: `image/${format}`});
  //       const url = URL.createObjectURL(blob);

  //       // 创建下载链接并触发点击
  //       const a = document.createElement('a');
  //       a.href = url;
  //       a.download = `export-${nodeId}.${format}`;
  //       document.body.appendChild(a);
  //       a.click();

  //       // 清理
  //       setTimeout(() => {
  //         document.body.removeChild(a);
  //         URL.revokeObjectURL(url);
  //       }, 100);
  //     }
  //   }
  //   window.addEventListener('message', messageHandler)
  //   return () => {
  //     window.removeEventListener('message', messageHandler)
  //   }
  // }, [show, nodeId])

  useEffect(() => {
    show && showPreview(nodeId)
  }, [show, nodeId])
  const handleImgScaleChange = useCallback((e: React.ChangeEvent<HTMLSelectElement>) => {
    const value = e.target.value
    imgScale.current = Number(value)
  }, [])
  const handlePngFormatChange = useCallback((e: React.ChangeEvent<HTMLSelectElement>) => {
    const value = e.target.value
    imgFormat.current = value
  }, [])

  const [compress, setCompress] = useState(true)
  return (
    <div>
      <div className="flex gap-2 mb-2 items-center">
        <Tooltip content={show ? '取消图层预览' : '图层预览'}>
          <button
            onClick={() => {
              previewImg(nodeId)
            }}
            className="p-1 text-xs rounded hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="16"
              height="16"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            >
              <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path>
              <circle cx="12" cy="12" r="3"></circle>
              {show && <line x1="1" y1="1" x2="23" y2="23" stroke="currentColor" strokeWidth="2" />}
            </svg>
          </button>
        </Tooltip>

        {/* 新增倍率下拉框 */}
        <select
          className="text-xs cursor-pointer p-1 rounded border border-gray-200 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700"
          defaultValue="1x"
          onChange={handleImgScaleChange}
        >
          {[1, 2, 3, 4].map(item => {
            return (
              <option key={item} value={item} className="hover:cursor-pointer">
                {item}x
              </option>
            )
          })}
        </select>

        {/* 新增格式下拉框 */}
        <select
          className="text-xs cursor-pointer p-1 rounded border border-gray-200  bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700"
          defaultValue="png"
          onChange={handlePngFormatChange}
        >
          <option value="png" className="hover:cursor-pointer">
            PNG
          </option>
          <option value="jpg" className="hover:cursor-pointer">
            JPG
          </option>
          <option value="svg" className="hover:cursor-pointer">
            SVG
          </option>
        </select>

        {/* 勾选是否压缩 */}
        {window.isInternal && (
          <label className="flex items-center text-xs ml-2">
            <input
              type="checkbox"
              checked={compress}
              onChange={e => setCompress(e.target.checked)}
              className="mr-1"
              disabled={imgFormat.current !== 'png'}
            />
            PNG压缩
          </label>
        )}
        <Tooltip content="下载切图">
          <button
            onClick={() => {
              downLoadImg({nodeId, scale: imgScale.current, format: imgFormat.current}, compress)
              f2cDataReport(ReportType.downloadCutCount, '', 1, {
                dim4: 'chrome',
                value2: 1,
              })
            }}
            className="p-1 text-xs rounded hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="16"
              height="16"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            >
              <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
              <polyline points="7 10 12 15 17 10"></polyline>
              <line x1="12" y1="15" x2="12" y2="3"></line>
            </svg>
          </button>
        </Tooltip>
        {/* <Tooltip content="PNG压缩预览">
          <button
            onClick={() => {
              handlePreviewClick(nodeId)
            }}
            className="p-1 text-xs rounded hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer"
          >
            <img src={'https://hd-static.yystatic.com/7420041393385394.png'} width={16} height={16} />
          </button>
        </Tooltip> */}
      </div>
      {show && (
        <div
          className="relative mt-4 flex-col-center rounded-sm mb-4"
          style={{
            background:
              'conic-gradient(#fff .25turn,#f7f7f7 .25turn .5turn,#fff .5turn .75turn,#f7f7f7 .75turn) top left/20px 20px repeat',
          }}
        >
          <SVG src={show} height="100%" className="min-w-20 p-5 max-h-210px max-w-60" />
        </div>
      )}
      {isSvg && <CodeCom highlightedCode={svgCode} copyResult={svg} />}
    </div>
  )
}
export default ExportCom
